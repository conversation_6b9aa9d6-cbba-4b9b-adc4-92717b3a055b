#ifndef QRCODEDETECTOR_H
#define QRCODEDETECTOR_H

#include <QString>
#include <QStringList>
#include <QDebug>
#include <opencv2/opencv.hpp>
// #include <opencv2/wechat_qrcode.hpp>  // 注释掉微信二维码模块

// ZXing库头文件
#include "ZXing/ReadBarcode.h"
#include "ZXing/BarcodeFormat.h"
#include "ZXing/ReaderOptions.h"

/**
 * @brief 条码检测器类
 * 使用ZXing库进行二维码和一维码识别（微信二维码检测器已注释）
 * 支持QR码、Code128、Code39、EAN13、UPC等多种格式
 */
class QRCodeDetector
{
public:
    /**
     * @brief 构造函数
     */
    QRCodeDetector();
    
    /**
     * @brief 析构函数
     */
    ~QRCodeDetector();
    
    /**
     * @brief 初始化检测器
     * @param modelPath 模型文件路径（ZXing不需要模型文件，此参数保留兼容性）
     * @return 初始化是否成功
     */
    bool initialize(const QString& modelPath = "");
    
    /**
     * @brief 检测图片中的二维码
     * @param imagePath 图片文件路径
     * @return 检测到的二维码内容列表
     */
    QStringList detectQRCodes(const QString& imagePath);
    
    /**
     * @brief 检测cv::Mat图像中的二维码
     * @param image OpenCV图像
     * @return 检测到的二维码内容列表
     */
    QStringList detectQRCodes(const cv::Mat& image);
    
    /**
     * @brief 检查检测器是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return m_initialized; }
    
    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    QString getLastError() const { return m_lastError; }

    /**
     * @brief 设置是否启用多角度旋转检测
     * @param enabled 是否启用
     */
    void setMultiAngleDetectionEnabled(bool enabled) { m_multiAngleEnabled = enabled; }

    /**
     * @brief 获取是否启用多角度旋转检测
     * @return 是否启用
     */
    bool isMultiAngleDetectionEnabled() const { return m_multiAngleEnabled; }

    /**
     * @brief 设置最大旋转尝试次数
     * @param maxAttempts 最大尝试次数
     */
    void setMaxRotationAttempts(int maxAttempts) { m_maxRotationAttempts = maxAttempts; }

    /**
     * @brief 获取最大旋转尝试次数
     * @return 最大尝试次数
     */
    int getMaxRotationAttempts() const { return m_maxRotationAttempts; }

    /**
     * @brief 设置是否启用基于定位图案的智能角度检测
     * @param enabled 是否启用
     */
    void setFinderPatternDetectionEnabled(bool enabled) { m_finderPatternEnabled = enabled; }

    /**
     * @brief 获取是否启用基于定位图案的智能角度检测
     * @return 是否启用
     */
    bool isFinderPatternDetectionEnabled() const { return m_finderPatternEnabled; }

private:
    // cv::wechat_qrcode::WeChatQRCode* m_detector;  ///< 微信二维码检测器（已注释）
    // cv::QRCodeDetector m_detector;                ///< 标准OpenCV二维码检测器（已注释）
    bool m_initialized;                           ///< 是否已初始化
    QString m_lastError;                          ///< 最后的错误信息
    bool m_multiAngleEnabled;                     ///< 是否启用多角度旋转检测
    int m_maxRotationAttempts;                    ///< 最大旋转尝试次数
    bool m_finderPatternEnabled;                  ///< 是否启用基于定位图案的智能角度检测
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const QString& error);

    /**
     * @brief 安全读取图片文件（支持中文路径）
     * @param imagePath 图片文件路径
     * @return OpenCV图像，如果读取失败则返回空图像
     */
    cv::Mat readImageSafely(const QString& imagePath);

    /**
     * @brief 内部条码检测方法（支持二维码和一维码）
     * @param image OpenCV图像
     * @return 检测到的条码内容列表（包括二维码和一维码）
     */
    QStringList detectQRCodesInternal(const cv::Mat& image);

    /**
     * @brief 使用多角度旋转进行二维码检测
     * @param image OpenCV图像
     * @return 检测到的条码内容列表
     */
    QStringList detectQRCodesWithRotation(const cv::Mat& image);

    /**
     * @brief 旋转图像到指定角度
     * @param image 输入图像
     * @param angle 旋转角度（度）
     * @return 旋转后的图像
     */
    cv::Mat rotateImage(const cv::Mat& image, double angle);

    /**
     * @brief 使用单一角度进行ZXing检测
     * @param image OpenCV图像
     * @return 检测到的条码内容列表
     */
    QStringList detectQRCodesSingleAttempt(const cv::Mat& image);

    /**
     * @brief 简化的检测方法（内存友好）
     * @param image OpenCV图像
     * @return 检测到的条码内容列表
     */
    QStringList detectQRCodesSimplified(const cv::Mat& image);

    /**
     * @brief 通过检测定位图案动态计算旋转角度
     * @param image OpenCV图像
     * @return 检测到的条码内容列表
     */
    QStringList detectQRCodesWithFinderPatterns(const cv::Mat& image);

    /**
     * @brief 检测二维码定位图案（三个角上的方形图案）
     * @param image 输入图像（灰度图）
     * @return 定位图案的中心点列表
     */
    std::vector<cv::Point2f> detectFinderPatterns(const cv::Mat& grayImage);

    /**
     * @brief 优化的定位图案检测（性能优先）
     * @param grayImage 输入图像（灰度图）
     * @return 定位图案的中心点列表
     */
    std::vector<cv::Point2f> detectFinderPatternsOptimized(const cv::Mat& grayImage);

    /**
     * @brief 简化的定位图案检测（基于形状匹配）
     * @param grayImage 输入图像（灰度图）
     * @return 定位图案的中心点列表
     */
    std::vector<cv::Point2f> detectFinderPatternsSimple(const cv::Mat& grayImage);

    /**
     * @brief 基于模板匹配的定位图案检测
     * @param grayImage 输入图像（灰度图）
     * @return 定位图案的中心点列表
     */
    std::vector<cv::Point2f> detectFinderPatternsTemplate(const cv::Mat& grayImage);

    /**
     * @brief 根据定位图案计算二维码的旋转角度
     * @param finderCenters 定位图案中心点（至少需要2个点）
     * @return 旋转角度（度）
     */
    double calculateRotationAngle(const std::vector<cv::Point2f>& finderCenters);

    /**
     * @brief 验证检测到的轮廓是否为定位图案
     * @param contour 轮廓
     * @param hierarchy 轮廓层次结构
     * @param contourIndex 当前轮廓索引
     * @return 是否为定位图案
     */
    bool isFinderPattern(const std::vector<cv::Point>& contour,
                        const std::vector<cv::Vec4i>& hierarchy,
                        int contourIndex);

    /**
     * @brief 优化的定位图案验证（性能优先）
     * @param contour 轮廓
     * @param hierarchy 轮廓层次结构
     * @param contourIndex 当前轮廓索引
     * @return 是否为定位图案
     */
    bool isFinderPatternOptimized(const std::vector<cv::Point>& contour,
                                 const std::vector<cv::Vec4i>& hierarchy,
                                 int contourIndex);
};

#endif // QRCODEDETECTOR_H
